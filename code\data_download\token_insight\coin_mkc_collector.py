import os
import polars as pl
import concurrent.futures
import time
from concurrent.futures import Thr<PERSON>PoolExecutor
import argparse
from queue import Queue
import threading

from coin_list_fetcher import fetch_coin_list, filter_by_symbol_list, save_to_parquet
from coin_history_fetcher import fetch_coin_history, save_to_parquet as save_history_to_parquet
from env_loader import TOKEN_INSIGHT_API_LIMIT

class CoinDataCollector:
    def __init__(self, 
                 workers=12, 
                 coins_cache_path="collected_coins.parquet",
                 symbol_list_path="symbol_list.csv",
                 interval_output_path="collected_coins_interval.parquet",
                 output_path="./",
                 coin_pool=1500,
                 fetch_limit=1500,
                 vs_currency="usd"):
        self.workers = workers
        self.coins_cache_path = coins_cache_path
        self.symbol_list_path = symbol_list_path
        self.interval_output_path = interval_output_path
        self.output_path = output_path + "_" + time.strftime("%Y%m%d_%H%M%S")
        self.coin_pool = min(coin_pool, 16000)  # 最大支持约16000个币种
        self.fetch_limit = min(fetch_limit, 1500)  # 单次请求最大1500个币种
        self.vs_currency = vs_currency
        
        # 创建输出目录（如果不存在）
        if not os.path.exists(self.output_path):
            os.makedirs(self.output_path, exist_ok=True)
        
        # 使用阻塞队列替代锁
        self.interval_queue = Queue()
        self.interval_data = []
        
        # API请求限制
        self.api_limit = TOKEN_INSIGHT_API_LIMIT
        self.request_times = []
        self.request_lock = threading.Lock()
        
        # 失败重试队列
        self.retry_queue = Queue()
        self.max_retries = 3  # 最大重试次数
        
        # 启动一个专门的线程处理间隔数据
        self.interval_processor = threading.Thread(target=self._process_interval_data)
        self.interval_processor.daemon = True
        self.interval_processor.start()

    def _process_interval_data(self):
        """处理间隔数据的专用线程"""
        while True:
            # 从队列中获取数据，这会阻塞直到队列中有数据
            item = self.interval_queue.get()
            
            # 特殊标记，表示处理完成
            if item is None:
                self.interval_queue.task_done()
                break
            
            self.interval_data.append(item)
            self.interval_queue.task_done()

    def check_and_update_rate_limit(self):
        """检查并更新API请求频率限制"""
        with self.request_lock:
            current_time = time.time()
            # 移除一分钟前的请求记录
            self.request_times = [t for t in self.request_times if current_time - t < 60]
            
            # 检查当前请求数是否达到限制
            if len(self.request_times) >= self.api_limit:
                # 计算需要等待的时间
                wait_time = 60 - (current_time - self.request_times[0])
                if wait_time > 0:
                    print(f"达到API限制，等待 {wait_time:.2f} 秒...")
                    time.sleep(wait_time)
                    # 清理过期的请求记录
                    self.request_times = [t for t in self.request_times if current_time + wait_time - t < 60]
            
            # 添加当前请求时间
            self.request_times.append(time.time())

    def get_coin_list(self):
        """获取符合条件的币种列表"""
        # 如果本地已缓存，直接读取并过滤
        if os.path.exists(self.coins_cache_path):
            print(f"使用本地缓存的币种列表: {self.coins_cache_path}")
            df = pl.read_parquet(self.coins_cache_path)
            # 对缓存的数据也进行过滤
            return filter_by_symbol_list(df, self.symbol_list_path)
        
        # 否则，分批次获取币种列表
        print(f"开始获取币种列表，目标数量: {self.coin_pool}")
        
        all_coins_df = None
        offset = 0
        batches_count = 0
        
        # 计算需要获取的批次数
        total_batches = (self.coin_pool + self.fetch_limit - 1) // self.fetch_limit
        
        while offset < self.coin_pool:
            batches_count += 1
            print(f"获取币种批次 {batches_count}/{total_batches}，偏移量: {offset}")
            
            # 检查并遵守API请求限制
            self.check_and_update_rate_limit()
            
            # 计算当前批次应获取的数量
            current_limit = min(self.fetch_limit, self.coin_pool - offset)
            
            # 获取当前批次的币种列表
            batch_df = fetch_coin_list(limit=current_limit, offset=offset, vs_currency=self.vs_currency)
            
            if batch_df is None or batch_df.is_empty():
                print(f"获取币种批次 {batches_count} 失败或返回空列表，将在后续重试")
                # 记录失败的请求参数，以便后续重试
                if not hasattr(self, 'retry_queue'):
                    self.retry_queue = []
                self.retry_queue.put((current_limit, offset, batches_count))
                # 继续处理下一批次，而不是中断整个过程
                offset += self.fetch_limit
                continue
            
            print(f"批次 {batches_count} 获取到 {len(batch_df)} 个币种")
            
            # 合并数据
            if all_coins_df is None:
                all_coins_df = batch_df
            else:
                all_coins_df = pl.concat([all_coins_df, batch_df])
            
            # 更新偏移量
            offset += self.fetch_limit
            
            # 如果返回的数据少于请求的数量，说明已经获取完所有可用的币种
            if len(batch_df) < current_limit:
                print(f"已获取所有可用币种，总数: {len(all_coins_df)}")
                break
        
        if not self.retry_queue.empty():
            print("开始处理重试队列...")
            retry_count = 0
            batch_df = None
            while not self.retry_queue.empty():
                limit, offset, batch_num = self.retry_queue.get()
                batch_df = fetch_coin_list(limit=limit, offset=offset, vs_currency=self.vs_currency)
                if batch_df is not None and not batch_df.is_empty():
                    all_coins_df = pl.concat([all_coins_df, batch_df])
                else:
                    print(f"重试批次 {batch_num} 失败或返回空列表，将在后续重试")
                retry_count += 1
        
        if all_coins_df is not None and not all_coins_df.is_empty():
            print(f"共获取 {len(all_coins_df)} 个币种")
            
            # 过滤并保存
            filtered_df = filter_by_symbol_list(all_coins_df, self.symbol_list_path)
            save_to_parquet(filtered_df, self.coins_cache_path)
            return filtered_df
        
        return None

    def process_coin(self, coin_id, symbol, retry_count=0):
        """处理单个币种的历史数据"""
        print(f"正在获取 {symbol}({coin_id}) 的历史数据..." + (f" 重试 #{retry_count}" if retry_count > 0 else ""))
        
        # 检查并遵守API请求限制
        self.check_and_update_rate_limit()
        
        df, symbol, first_timestamp, last_timestamp, current_market_cap = fetch_coin_history(coin_id)
        
        if df is not None and not df.is_empty():
            # 构建输出路径
            output_file = f"{symbol}_{coin_id}_history_{first_timestamp}_{last_timestamp}.parquet"
            full_output_path = os.path.join(self.output_path, output_file)
            
            # 保存历史数据
            save_history_to_parquet(df, symbol, coin_id, first_timestamp, last_timestamp, self.output_path)
            
            # 使用队列安全地更新间隔数据，无需显式锁
            self.interval_queue.put({
                "symbol": symbol,
                "id": coin_id,
                "first_timestamp": first_timestamp,
                "last_timestamp": last_timestamp,
                "current_market_cap": current_market_cap,
                "file_path": full_output_path
            })
            
            return True, symbol, coin_id
        else:
            # 添加到重试队列
            if retry_count < self.max_retries:
                print(f"获取 {symbol}({coin_id}) 数据失败，添加到重试队列")
                self.retry_queue.put((coin_id, symbol, retry_count + 1))
            else:
                print(f"获取 {symbol}({coin_id}) 数据失败，已达最大重试次数")
        
        return False, symbol, coin_id

    def save_interval_data(self):
        """保存币种时间间隔数据"""
        # 发送结束信号给处理线程
        self.interval_queue.put(None)
        # 等待所有任务完成
        self.interval_queue.join()
        self.interval_processor.join()
        
        if not self.interval_data:
            print("没有间隔数据可保存")
            return
        
        df = pl.DataFrame(self.interval_data)
        full_interval_path = os.path.join(self.output_path, self.interval_output_path)
        df.write_parquet(full_interval_path)
        print(f"币种时间间隔数据已保存到 {full_interval_path}")
        print(f"收集了 {len(self.interval_data)} 个币种的时间间隔数据")

    def process_retry_queue(self):
        """处理重试队列中的币种"""
        if self.retry_queue.empty():
            return 0
        
        print("开始处理重试队列...")
        retry_count = 0
        
        # 处理重试队列中的所有币种
        while not self.retry_queue.empty():
            coin_id, symbol, retry_num = self.retry_queue.get()
            self.process_coin(coin_id, symbol, retry_num)
            retry_count += 1
        
        return retry_count

    def run(self):
        """运行主程序"""
        # 获取币种列表
        coins_df = self.get_coin_list()
        
        if coins_df is None or coins_df.is_empty():
            print("未获取到符合条件的币种列表")
            return
        
        print(f"获取到 {len(coins_df)} 个符合条件的币种")
        
        # 提取币种ID和符号
        coins_data = coins_df.select(["id", "symbol"]).to_dicts()
        
        # 使用线程池处理每个币种
        successful = 0
        failed = 0
        
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=self.workers) as executor:
            futures = [executor.submit(self.process_coin, coin["id"], coin["symbol"]) for coin in coins_data]
            
            for future in concurrent.futures.as_completed(futures):
                try:
                    result, symbol, coin_id = future.result()
                    if result:
                        successful += 1
                        print(f"成功处理 {symbol}({coin_id})")
                    else:
                        failed += 1
                        print(f"处理 {symbol}({coin_id}) 失败，已加入重试队列")
                except Exception as e:
                    failed += 1
                    print(f"处理币种时出错: {e}")
        
        # 处理重试队列
        retry_count = 0
        while not self.retry_queue.empty():
            retry_processed = self.process_retry_queue()
            retry_count += retry_processed
            if retry_processed == 0:
                break
        
        end_time = time.time()
        
        # 保存时间间隔数据
        self.save_interval_data()
        
        print(f"任务完成. 总计: {len(coins_data)}, 成功: {successful}, 失败: {failed}, 重试: {retry_count}")
        print(f"耗时: {end_time - start_time:.2f} 秒")

def main():
    parser = argparse.ArgumentParser(description="加密货币数据收集器")
    parser.add_argument("--workers", type=int, default=2, help="线程池工作线程数")
    parser.add_argument("--coins_cache", type=str, default="collected_coins.parquet", help="币种缓存文件路径")
    parser.add_argument("--symbol_list", type=str, default="symbol_list.csv", help="符号列表文件路径")
    parser.add_argument("--interval_output", type=str, default="collected_coins_interval.parquet", 
                        help="间隔数据输出文件路径")
    parser.add_argument("--output_path", type=str, default="./coins_mkc_history", help="输出数据存放目录")
    parser.add_argument("--coin_pool", type=int, default=15704, help="要获取的最大币种数量，最大约15704")
    parser.add_argument("--fetch_limit", type=int, default=1500, help="每次API请求获取的币种数量，最大1500")
    parser.add_argument("--vs_currency", type=str, default="usd", help="计价货币，默认为usd")
    
    args = parser.parse_args()
    
    collector = CoinDataCollector(
        workers=args.workers, 
        coins_cache_path=args.coins_cache,
        symbol_list_path=args.symbol_list,
        interval_output_path=args.interval_output,
        output_path=args.output_path,
        coin_pool=args.coin_pool,
        fetch_limit=args.fetch_limit,
        vs_currency=args.vs_currency
    )
    
    collector.run()

if __name__ == "__main__":
    main()