import os
from dotenv import load_dotenv

# 获取当前脚本所在的目录
current_dir = os.path.dirname(os.path.abspath(__file__))
# 构建.env文件的绝对路径
dotenv_path = os.path.join(current_dir, '.env')
# 加载.env文件中的环境变量
load_dotenv(dotenv_path)

# 打印环境变量加载情况，调试用
print(f"环境变量加载自: {dotenv_path}")

# API端点
TOKEN_INSIGHT_GET_COIN_LIST = os.getenv('TOKEN_INSIGHT_GET_COIN_LIST')
TOKEN_INSIGHT_GET_COIN_HISTORY = os.getenv('TOKEN_INSIGHT_GET_COIN_HISTORY')
TOKEN_INSIGHT_API_KEY = os.getenv('TOKEN_INSIGHT_API_KEY')
TOKEN_INSIGHT_API_LIMIT = int(os.getenv('TOKEN_INSIGHT_API_LIMIT', '60'))

# 打印环境变量值，调试用
# print(f"TOKEN_INSIGHT_GET_COIN_LIST: {TOKEN_INSIGHT_GET_COIN_LIST}")
# print(f"TOKEN_INSIGHT_GET_COIN_HISTORY: {TOKEN_INSIGHT_GET_COIN_HISTORY}")
# print(f"TOKEN_INSIGHT_API_KEY: {TOKEN_INSIGHT_API_KEY}")
# print(f"TOKEN_INSIGHT_API_LIMIT: {TOKEN_INSIGHT_API_LIMIT}")

def get_api_headers():
    """
    获取API请求需要的headers
    """
    return {
        'TI_API_KEY': TOKEN_INSIGHT_API_KEY,
        'accept': 'application/json'
    }