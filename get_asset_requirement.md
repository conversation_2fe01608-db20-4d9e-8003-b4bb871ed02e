## 一、任务概述

现在我有两个API：

1. get：https://api.tokeninsight.com/api/v1/coins/list

2. get:
https://api.tokeninsight.com/api/v1/history/coins/{id}

我希望先使用coins/list这个API获取所有可用的币种的id和symbol，并通过symbol列与本地的文件symbol_list.csv做一个交集，筛选出一部分币种的id，然后使用这个id的合集，用里面的每一个id调用/history/coins/{id}这个API，获取每一个选中的币的历史数据。

## 二、接口详情

### Retrieve coins list
- URL： get
https://api.tokeninsight.com/api/v1/coins/list
- Response:
{
  "status": {
    "code": 0,
    "message": "",
    "timestamp": 1744196429369
  },
  "data": {
    "items": [
      {
        "price": 77299.56855364113,
        "name": "Bitcoin",
        "symbol": "BTC",
        "id": "bitcoin",
        "logo": "https://s2.tokeninsight.com/static/coins/img/currency/Bitcoin_BTC.png?a=1666955723",
        "spot_volume_24h": 22293158528.199314,
        "price_change_percentage_24h": -0.0221814652,
        "url": "https://tokeninsight.com/en/coins/bitcoin/overview"
      },    ],
    "page_info": {
      "total_results": 15704
    }
  }
}
- Query Params：

    limit (integer):  default 300, max 1500

    offset (integer):  default 0

    vs_currency (string) usd in default

### Coin history data
- URL: get https://api.tokeninsight.com/api/v1/history/coins/{id}
- Response: 
{
  "status": {
    "code": 0,
    "message": "",
    "timestamp": 1744198319857
  },
  "data": {
    "name": "Bitcoin",
    "symbol": "BTC",
    "id": "bitcoin",
    "logo": "https://s2.tokeninsight.com/static/coins/img/currency/Bitcoin_BTC.png?a=1666955723",
    "market_chart": [
      {
        "price": 76256.8959211195,
        "vol_spot_24h": 18519286520.1254,
        "market_cap": 1513594988343.706,
        "timestamp": 1744070400000
      }
    ],
    "vs_currency": "usd"
  }
}
- Path Params:
    id (string) required
    id of coins; e.g. bitcoin

- Query Params:

    interval (string)
    minute, hour, day day in default day
    
    length (integer)
    minute: 1-10080(60 in default) hour: 1-8784(24 in default) day: 1-365,-1 stands for max (90 in default)

    vs_currency (string)
    usd in default

## 三、具体要求

（考虑到之后这个脚本可能集成到选股机器人的数据筛选模块中）

我希望使用python编写脚本，完成数据的获取。调用coin/list接口的方法最好封装成独立的一个文件，并且支持独立运行。独立运行的情况下支持以query params作为运行参数，输出一个collected_coins.parquet，文件包含price, symbol, id, spot_volume_24h，price_change_percentage_24这五个列。

而调用history/coins/{id}接口的方法同样封装成独立的一个文件，支持独立运行。独立运行的情况下支持以path params和query params作为运行参数，并输出一个{symbol}_{id}_history_firsttimestamp_lasttimestamp.parquet，文件包含price, vol_spot_24h, market_cap, timestamp四个列。

完成本次任务的脚本独立于上面的两个文件，但是调用上面两个文件内的方法。如果本地已经存在collected_coins.parquet,则优先使用本地缓存获取id和symbol；否则先使用coins/list这个方法获取所有可用的币种的id和symbol，并通过symbol列与本地的文件symbol_list.csv做一个交集，筛选出一部分币种的id，然后使用这个id的合集，用里面的每一个id调用/history/coins/{id}这个方法，获取每一个选中的币的历史数据，query param里的length直接选-1，获取最大值（本过程允许使用多线程，但是要留好worker设置的参数）。

在调用/history/coins/{id}方法时，同时记录每一个symbol的起始timestamp和结束timestamp（如果获取数据时使用了多线程，请你自行设计算法解决竞争问题，可以参考blockingque或者concurrent hash map之类的方法，尽量不要因为颗粒度太大的锁影响多线程性能），把这个信息存到一张新的表collected_coins_interval.parquet中，表内含有symbol，id，start_timestamp, end_timestamp, current_market_cap5列。

数据的读写、变形操作尽量使用polars库

